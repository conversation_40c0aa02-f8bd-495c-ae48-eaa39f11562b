// Export all schemas from a central location
export { lessonSchema, type LessonFormData } from "./lesson.schema";
export { chapterSchema, type ChapterFormData } from "./chapter.schema";
export { formSchema, type FormData } from "./form.schema";
export {
  gradeItemSchema,
  gradeFormSchema,
  type GradeItemFormData,
  type GradeFormData,
} from "./grade.schema";
export {
  academicYearSchema,
  type AcademicYearFormData,
} from "./academicYear.schema";
export { frameworkSchema, type FrameworkFormData } from "./framework.schema";
export { tagSchema, type TagData } from "./tag.schema";
