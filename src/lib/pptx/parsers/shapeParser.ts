import { extractTransform } from "../utils/transform";
import { generateSvgPath } from "../utils/svgGenerator";

// Parse shape properties from XML
export const parseShapeProperties = (spPr: any, childrenSvg?: string): any => {
  try {
    // Extract transform information (position and size)
    const transform = extractTransform(spPr);
    const { x, y, width = 100, height = 100 } = transform;

    // Extract shape type from preset or custom geometry
    const prstGeom = spPr["a:prstGeom"]?.[0];
    const custGeom = spPr["a:custGeom"]?.[0];
    let shapeType = "rect"; // Default fallback

    if (prstGeom) {
      // Try different ways to access the preset type
      shapeType =
        prstGeom.$?.prst || prstGeom.$.prst || prstGeom.prst || "rect";
      console.log("Found prstGeom:", shapeType);
    } else if (custGeom) {
      shapeType = "custom";
      console.log("Found custom geometry");
    } else {
      console.log("No geometry found, defaulting to rect");
    }

    // Extract fill color
    let fill = "#FFFFFF"; // Default white

    // Try solid fill
    const solidFill = spPr["a:solidFill"]?.[0];
    if (solidFill) {
      const srgbClr = solidFill["a:srgbClr"]?.[0]?.$?.val;
      if (srgbClr) {
        fill = `#${srgbClr}`;
      }

      // Try scheme color
      const schemeClr = solidFill["a:schemeClr"]?.[0]?.$?.val;
      if (schemeClr && !srgbClr) {
        // Map common scheme colors
        const schemeColorMap: { [key: string]: string } = {
          accent1: "#4472C4",
          accent2: "#E7E6E6",
          accent3: "#A5A5A5",
          accent4: "#FFC000",
          accent5: "#5B9BD5",
          accent6: "#70AD47",
          dk1: "#000000",
          lt1: "#FFFFFF",
          dk2: "#44546A",
          lt2: "#E7E6E6",
        };
        fill = schemeColorMap[schemeClr] || "#CCCCCC";
      }
    }

    // Extract outline/border
    let border = "none";
    const ln = spPr["a:ln"]?.[0];
    if (ln) {
      const w = ln.$?.w ? Math.round(parseInt(ln.$.w) / 12700) : 1;
      const solidFillLn = ln["a:solidFill"]?.[0];
      let borderColor = "#000000";

      if (solidFillLn) {
        const srgbClr = solidFillLn["a:srgbClr"]?.[0]?.$?.val;
        if (srgbClr) {
          borderColor = `#${srgbClr}`;
        }
      }

      border = `${w}px solid ${borderColor}`;
    }

    // Generate SVG path (pass raw XML for custom geometry)
    const svgPath = generateSvgPath(
      shapeType,
      width,
      height,
      childrenSvg,
      spPr
    );

    return {
      type: shapeType,
      x,
      y,
      width,
      height,
      fill,
      border,
      svgPath,
    };
  } catch (error) {
    console.error("Error parsing shape properties:", error);
    return null;
  }
};
