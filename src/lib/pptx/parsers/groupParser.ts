import { PptxSlide, ExtractedImage } from "../types";
import { extractTransform } from "../utils/transform";

// Process group shape as single entity (no nested parsing)
export const processGroupShape = (
  group: any,
  slide: PptxSlide,
  _extractedImages: ExtractedImage[],
  parentTransform: { x: number; y: number } = { x: 0, y: 0 }
) => {
  const groupTransform = extractTransform(group["p:grpSpPr"]?.[0]) || {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
  };

  const combinedTransform = {
    x: parentTransform.x + groupTransform.x,
    y: parentTransform.y + groupTransform.y,
    width: groupTransform.width || 100,
    height: groupTransform.height || 100,
  };

  // Extract group shapeId for tracing
  const shapeId = group?.["p:nvGrpSpPr"]?.[0]?.["p:cNvPr"]?.[0]?.$?.id;

  console.log(
    `Parsed group shape at (${combinedTransform.x}, ${
      combinedTransform.y
    }) size ${combinedTransform.width}x${combinedTransform.height}${
      shapeId ? ` (id: ${shapeId})` : ""
    }`
  );

  // Add group as single shape (no nested parsing)
  slide.shapes.push({
    type: "group",
    x: combinedTransform.x,
    y: combinedTransform.y,
    width: combinedTransform.width,
    height: combinedTransform.height,
    fill: "transparent",
    border: "none",
    svgPath: undefined, // Placeholder - group rendered as single entity
    shapeId: shapeId,
  });
};
