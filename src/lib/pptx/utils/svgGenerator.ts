// SVG path mapping for supported shapes
const SVG_PATH_MAP: Record<string, (w: number, h: number) => string> = {
  ellipse: (w, h) =>
    `<ellipse cx="${w / 2}" cy="${h / 2}" rx="${w / 2}" ry="${h / 2}" />`,
  rect: (w, h) => `<rect width="${w}" height="${h}" />`,
  roundRect: (w, h) => {
    const rx = Math.min(w, h) * 0.1;
    return `<rect x="0" y="0" width="${w}" height="${h}" rx="${rx}" ry="${rx}" />`;
  },
  triangle: (w, h) => `<polygon points="${w / 2},0 ${w},${h} 0,${h}" />`,
  diamond: (w, h) =>
    `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${h / 2}" />`,
  rightArrow: (w, h) =>
    `<polygon points="0,${h * 0.3} ${w * 0.7},${h * 0.3} ${w * 0.7},0 ${w},${
      h / 2
    } ${w * 0.7},${h} ${w * 0.7},${h * 0.7} 0,${h * 0.7}" />`,
  leftArrow: (w, h) =>
    `<polygon points="0,${h / 2} ${w * 0.3},0 ${w * 0.3},${h * 0.3} ${w},${
      h * 0.3
    } ${w},${h * 0.7} ${w * 0.3},${h * 0.7} ${w * 0.3},${h}" />`,
  star5: (w, h) => {
    const centerX = w / 2;
    const centerY = h / 2;
    const outerRadius = Math.min(w, h) / 2;
    const innerRadius = outerRadius * 0.4;
    let points = "";
    for (let i = 0; i < 10; i++) {
      const angle = (i * Math.PI) / 5 - Math.PI / 2;
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      points += `${x},${y} `;
    }
    return `<polygon points="${points.trim()}" />`;
  },
  plus: (w, h) =>
    `<path d="M ${w * 0.4} 0 L ${w * 0.6} 0 L ${w * 0.6} ${h * 0.4} L ${w} ${
      h * 0.4
    } L ${w} ${h * 0.6} L ${w * 0.6} ${h * 0.6} L ${w * 0.6} ${h} L ${
      w * 0.4
    } ${h} L ${w * 0.4} ${h * 0.6} L 0 ${h * 0.6} L 0 ${h * 0.4} L ${w * 0.4} ${
      h * 0.4
    } Z" />`,
  heart: (w, h) =>
    `<path d="M ${w / 2} ${h * 0.8} C ${w / 2} ${h * 0.8} 0 ${h * 0.4} 0 ${
      h * 0.25
    } C 0 ${h * 0.1} ${w * 0.25} 0 ${w / 2} ${h * 0.3} C ${w * 0.75} 0 ${w} ${
      h * 0.1
    } ${w} ${h * 0.25} C ${w} ${h * 0.4} ${w / 2} ${h * 0.8} ${w / 2} ${
      h * 0.8
    } Z" />`,
  hexagon: (w, h) => {
    const hexPoints = [];
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const x = w / 2 + (w / 2) * Math.cos(angle);
      const y = h / 2 + (h / 2) * Math.sin(angle);
      hexPoints.push(`${x},${y}`);
    }
    return `<polygon points="${hexPoints.join(" ")}" />`;
  },
  flowChartProcess: (w, h) =>
    `<rect x="0" y="0" width="${w}" height="${h}" rx="4" ry="4" />`,
  flowChartDecision: (w, h) =>
    `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${h / 2}" />`,
  pentagon: (w, h) => {
    const points = [];
    for (let i = 0; i < 5; i++) {
      const angle = (i * 2 * Math.PI) / 5 - Math.PI / 2;
      const x = w / 2 + (w / 2) * Math.cos(angle);
      const y = h / 2 + (h / 2) * Math.sin(angle);
      points.push(`${x},${y}`);
    }
    return `<polygon points="${points.join(" ")}" />`;
  },
  octagon: (w, h) => {
    const points = [];
    for (let i = 0; i < 8; i++) {
      const angle = (i * 2 * Math.PI) / 8 - Math.PI / 2;
      const x = w / 2 + (w / 2) * Math.cos(angle);
      const y = h / 2 + (h / 2) * Math.sin(angle);
      points.push(`${x},${y}`);
    }
    return `<polygon points="${points.join(" ")}" />`;
  },
  parallelogram: (w, h) => {
    const offset = w * 0.2;
    return `<polygon points="${offset},0 ${w},0 ${w - offset},${h} 0,${h}" />`;
  },
  trapezoid: (w, h) => {
    const topOffset = w * 0.2;
    return `<polygon points="${topOffset},0 ${
      w - topOffset
    },0 ${w},${h} 0,${h}" />`;
  },
  cloud: (w, h) =>
    `<path d="M ${w * 0.2} ${h * 0.6} C ${w * 0.1} ${h * 0.4} ${w * 0.1} ${
      h * 0.2
    } ${w * 0.3} ${h * 0.2} C ${w * 0.3} ${h * 0.1} ${w * 0.5} ${h * 0.1} ${
      w * 0.6
    } ${h * 0.2} C ${w * 0.8} ${h * 0.1} ${w * 0.9} ${h * 0.3} ${w * 0.8} ${
      h * 0.4
    } C ${w * 0.9} ${h * 0.6} ${w * 0.8} ${h * 0.8} ${w * 0.6} ${h * 0.7} C ${
      w * 0.4
    } ${h * 0.8} ${w * 0.2} ${h * 0.8} ${w * 0.2} ${h * 0.6} Z" />`,
};

// Custom path parser inline (simplified)
const parseCustomPathToSvg = (
  pathXml: any,
  width: number,
  height: number
): string => {
  try {
    const pathNode = pathXml?.["a:pathLst"]?.[0]?.["a:path"]?.[0];
    if (!pathNode) return "";

    const w = parseInt(pathNode.$?.w) || 21600;
    const h = parseInt(pathNode.$?.h) || 21600;
    const scaleX = width / w;
    const scaleY = height / h;

    const commands: string[] = [];
    const pointStr = (pt: any) => {
      const x = parseInt(pt.$.x || "0") * scaleX;
      const y = parseInt(pt.$.y || "0") * scaleY;
      return `${x.toFixed(2)},${y.toFixed(2)}`;
    };

    for (const key in pathNode) {
      if (!pathNode[key] || key === "$") continue;
      const items = Array.isArray(pathNode[key])
        ? pathNode[key]
        : [pathNode[key]];

      items.forEach((item: any) => {
        switch (key) {
          case "a:moveTo":
            const ptM = item["a:pt"]?.[0];
            if (ptM) commands.push(`M ${pointStr(ptM)}`);
            break;
          case "a:lnTo":
            const ptL = item["a:pt"]?.[0];
            if (ptL) commands.push(`L ${pointStr(ptL)}`);
            break;
          case "a:close":
            commands.push("Z");
            break;
        }
      });
    }

    return commands.length > 0 ? `<path d="${commands.join(" ")}" />` : "";
  } catch (error) {
    console.error("Error parsing custom path:", error);
    return "";
  }
};

// Generate SVG path for shape type
export const generateSvgPath = (
  shapeType: string,
  width: number,
  height: number,
  childrenSvg?: string,
  rawShapeXml?: any // Raw XML for custom geometry parsing
): string => {
  const w = width;
  const h = height;

  // 1. ƯU TIÊN: Nếu có path geometry XML → DÙNG NGAY (bất kể shapeType)
  const custGeom = rawShapeXml?.["a:custGeom"]?.[0];
  const prstGeom = rawShapeXml?.["a:prstGeom"]?.[0];
  const hasCustomPath = custGeom?.["a:pathLst"] || prstGeom?.["a:pathLst"];

  if (hasCustomPath) {
    const pathXml = custGeom || prstGeom;
    console.log(
      `Found pathLst in ${
        custGeom ? "custGeom" : "prstGeom"
      } for shapeType: ${shapeType}`
    );
    const svg = parseCustomPathToSvg(pathXml, w, h);
    if (svg) {
      return svg;
    }
  }

  // 2. Nếu là group
  if (shapeType === "group") {
    return childrenSvg || `<g></g>`;
  }

  // 3. Nếu là custom và có children
  if (shapeType === "custom" && childrenSvg) {
    return `<g>${childrenSvg}</g>`;
  }

  // 4. Nếu có mapping → dùng (fallback cho shapes không có pathLst)
  const shapeFn = SVG_PATH_MAP[shapeType];
  if (shapeFn) {
    console.log(`Using hardcoded SVG for shapeType: ${shapeType}`);
    return shapeFn(w, h);
  }

  // 5. Cuối cùng: fallback debug shape
  console.warn(
    `No path data found for shapeType: ${shapeType}, using fallback rect`
  );
  return `<rect width="${w}" height="${h}" fill="#eee" stroke="#999" stroke-width="1" />
          <text x="${w / 2}" y="${h / 2}" font-size="${
    Math.min(w, h) * 0.1
  }" fill="#666"
                text-anchor="middle" dominant-baseline="middle">
            ${shapeType}
          </text>`;
};
