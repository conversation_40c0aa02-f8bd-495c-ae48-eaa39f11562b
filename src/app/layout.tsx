import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { AppProvider } from "@/store";
import { Suspense } from "react";
import { Toaster } from "@/components/ui/sonner";
import ReactQueryProvider from "@/components/providers/ReactQueryProvider";
import Loading from "@/components/ui/loading";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});
const manrope = Manrope({
  variable: "--font-manrope",
  subsets: ["latin"],
});

const questrial = Questrial({
  weight: "400",
  variable: "--font-questrial",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> - Plan Your Journey",
  description: "<PERSON><PERSON><PERSON> helps you plan and organize your trips efficiently",
  icons: {
    icon: "/images/logoPlanbook.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/images/logoPlanbook.png" type="image/png" />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Cal+Sans&display=swap"
          rel="stylesheet"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${questrial.variable} ${manrope.variable} antialiased`}
      >
        <ReactQueryProvider>
          <Suspense fallback={<Loading />}>
            <Toaster position="top-right" />
            <AppProvider>{children}</AppProvider>
          </Suspense>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
