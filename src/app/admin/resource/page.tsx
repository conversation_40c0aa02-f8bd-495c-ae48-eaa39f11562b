"use client";
import { useState } from "react";
import dynamic from "next/dynamic";

import CreateBookModal from "@/components/organisms/create-book-modal";
import BookTable from "@/components/organisms/book-list";
import GradeTable from "@/components/organisms/grade-list";
import { Row } from "@tanstack/react-table";
import { Button } from "@/components/ui/Button";
import { toast } from "sonner";
import { BookResponse } from "@/types";
import { useRouter } from "next/navigation";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import SubjectTable from "@/components/organisms/subject-list";
import { useUpdateBookStatus } from "@/services/bookServices";

// Dynamic imports to avoid SSR issues
const CreateGradeModal = dynamic(() => import("@/components/organisms/create-grade-modal"), {
  ssr: false,
});
const CreateSubjectModal = dynamic(() => import("@/components/organisms/create-subject-modal"), {
  ssr: false,
});

const ResourceManagementPage = () => {
  const [selected, setSelected] = useState<Row<BookResponse>[]>([]);
  const updateBookStatusMutation = useUpdateBookStatus();
  const router = useRouter();
  const handleDelete = () => {
    if (selected.length > 1) {
      toast.error("Vui lòng chỉ chọn 1 sách");
    } else {
      const book = selected[0].original;
      const newStatus = book.status === "ACTIVE" ? "INACTIVE" : "ACTIVE";

      updateBookStatusMutation.mutate(
        {
          id: String(book.id),
          field: "status",
          queryParams: { newStatus }, // ✅ dùng biến động },
        },
        {
          onSuccess: () => {
            toast.success(
              newStatus === "INACTIVE"
                ? `Đã xoá sách ${book.name}`
                : `Đã khôi phục sách ${book.name}`
            );
          },
          onError: () => toast.error("Cập nhật trạng thái thất bại"),
        }
      );
    }
  };

  const handleEdit = () => {
    if (selected.length > 1) {
      toast.error("Vui lòng chỉ chọn 1 sách");
    } else {
      router.push(`/admin/resource/${selected[0].original.id}/content`);
    }
  };

  const tabs = [
    {
      value: "grade",
      label: "Quản lí khối",
      content: (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h1 className="font-calsans text-base">Danh sách khối</h1>
            <CreateGradeModal />
          </div>
          <GradeTable />
        </div>
      ),
    },
    {
      value: "subject",
      label: "Quản lí môn",
      content: (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h1 className="font-calsans text-base">Danh sách môn</h1>
            <CreateSubjectModal />
          </div>
          <SubjectTable />
        </div>
      ),
    },
    {
      value: "book",
      label: "Quản lí sách",
      content: (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h1 className="font-calsans text-base">Danh sách sách</h1>
            {selected.length > 0 ? (
              <div className="flex gap-1.5 items-center">
                <p className="text-sm text-muted-foreground pr-2.5">
                  Đã chọn {selected.length}
                </p>
                <Button onClick={handleDelete}>
                  {selected[0].original.status === "ACTIVE"
                    ? "Xóa"
                    : "Khôi phục"}
                </Button>
                <Button onClick={handleEdit} variant={"outline"}>
                  Chỉnh sửa
                </Button>
              </div>
            ) : (
              <CreateBookModal />
            )}
          </div>
          <BookTable
            onSelectionChange={(rows) => {
              setSelected(rows);
            }}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-5 w-full">
      <Tabs defaultValue="grade" className="w-full">
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default ResourceManagementPage;
